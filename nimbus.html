<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nimbus - Cyberpunk Event Planner</title>
    <style>
        :root {
            --primary-bg: #1a1a2e;
            --secondary-bg: #16213e;
            --accent-color: #00bfff;
            --success-color: #39ff14;
            --warning-color: #ffaa00;
            --error-color: #ff4757;
            --text-primary: #ffffff;
            --text-secondary: #b8c5d6;
            --spacing-sm: 0.5rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --spacing-xl: 2rem;
            --spacing-2xl: 3rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Orbitron', 'Rajdhani', sans-serif;
            background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 100%);
            color: var(--text-primary);
            min-height: 100vh;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--spacing-xl);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .welcome-content {
            text-align: center;
            max-width: 800px;
        }

        .welcome-title {
            font-size: 4rem;
            font-weight: 900;
            color: var(--accent-color);
            text-shadow: 0 0 30px var(--accent-color);
            margin-bottom: var(--spacing-lg);
            animation: glowPulse 3s ease-in-out infinite;
        }

        @keyframes glowPulse {
            0%, 100% { text-shadow: 0 0 30px var(--accent-color); }
            50% { text-shadow: 0 0 50px var(--accent-color), 0 0 70px var(--accent-color); }
        }

        .welcome-subtitle {
            font-size: 1.25rem;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-2xl);
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .auth-buttons {
            display: flex;
            gap: var(--spacing-lg);
            flex-wrap: wrap;
            justify-content: center;
            margin-bottom: var(--spacing-xl);
        }

        .btn {
            background: linear-gradient(45deg, var(--accent-color), #0080ff);
            border: none;
            padding: 15px 30px;
            border-radius: 12px;
            color: white;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 191, 255, 0.4);
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 191, 255, 0.6);
        }

        .btn-primary {
            background: linear-gradient(45deg, var(--accent-color), #0080ff);
        }

        .card {
            background: rgba(0, 191, 255, 0.1);
            border: 1px solid var(--accent-color);
            border-radius: 16px;
            padding: var(--spacing-xl);
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 191, 255, 0.3);
            margin: var(--spacing-lg) 0;
        }

        .auth-form {
            max-width: 400px;
            margin: 0 auto;
        }

        .auth-form h2 {
            color: var(--accent-color);
            margin-bottom: var(--spacing-lg);
            text-align: center;
        }

        .form-group {
            margin-bottom: var(--spacing-md);
        }

        .form-input {
            width: 100%;
            padding: 12px;
            background: rgba(0, 191, 255, 0.1);
            border: 1px solid var(--accent-color);
            border-radius: 8px;
            color: var(--text-primary);
            font-size: 16px;
        }

        .form-input:focus {
            outline: none;
            box-shadow: 0 0 10px rgba(0, 191, 255, 0.5);
        }



        .grid {
            display: grid;
            gap: var(--spacing-lg);
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .status-message {
            padding: var(--spacing-md);
            border-radius: 8px;
            margin: var(--spacing-md) 0;
            text-align: center;
        }

        .status-success {
            background: rgba(57, 255, 20, 0.1);
            border: 1px solid var(--success-color);
            color: var(--success-color);
        }

        .status-error {
            background: rgba(255, 71, 87, 0.1);
            border: 1px solid var(--error-color);
            color: var(--error-color);
        }

        .hidden {
            display: none;
        }

        /* OTP Verification Styles */
        .otp-input {
            text-align: center !important;
            font-size: 1.5rem !important;
            letter-spacing: 0.5rem !important;
            font-weight: bold;
            background: rgba(0, 191, 255, 0.1) !important;
            border: 2px solid var(--accent-color) !important;
        }

        .otp-input:focus {
            box-shadow: 0 0 15px rgba(0, 191, 255, 0.5) !important;
            border-color: var(--accent-color) !important;
        }

        /* Google Sign In Button */
        .btn-google {
            background: #ffffff !important;
            color: #333333 !important;
            border: 1px solid #dadce0 !important;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-google:hover {
            background: #f8f9fa !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
            transform: translateY(-1px);
        }

        .btn-google:active {
            background: #f1f3f4 !important;
            transform: translateY(0);
        }

        /* Divider */
        .divider {
            display: flex;
            align-items: center;
            margin: 1.5rem 0;
            color: var(--text-secondary);
        }

        .divider::before,
        .divider::after {
            content: '';
            flex: 1;
            height: 1px;
            background: rgba(255, 255, 255, 0.2);
        }

        .divider span {
            padding: 0 1rem;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .welcome-title {
                font-size: 2.5rem;
            }
            
            .auth-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .container {
                padding: var(--spacing-md);
            }
        }
    </style>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Supabase -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body>
    <div class="container">
        <!-- Welcome Screen -->
        <div id="welcome-screen" class="welcome-content">
            <h1 class="welcome-title">NIMBUS</h1>
            <p class="welcome-subtitle">
                Plan your events with futuristic style. Experience the power of cyberpunk aesthetics 
                combined with intelligent event management and personalization.
            </p>
            
            <div class="auth-buttons">
                <button class="btn btn-primary" onclick="showAuth()">
                    🚀 Get Started
                </button>
                <button class="btn" onclick="showDemo()">
                    🎮 View Demo
                </button>
            </div>
            
            <div class="card">
                <h3>🎮 Interactive Demo</h3>
                <p>
                    Welcome to Nimbus - your cyberpunk event planning platform! Click any button above to explore 
                    the futuristic interface. This version includes full Supabase integration, 
                    real-time updates, weather alerts, and complete event management.
                </p>
            </div>
        </div>

        <!-- Auth Screen -->
        <div id="auth-screen" class="welcome-content hidden">
            <h1 class="welcome-title">NIMBUS</h1>
            <div class="card">
                <div class="auth-form">
                    <h2 id="auth-title">Sign In to Nimbus</h2>
                    <!-- Google Sign In Button -->
                    <button onclick="signInWithGoogle()" class="btn btn-google" style="width: 100%; margin-bottom: 1rem;">
                        <svg width="20" height="20" viewBox="0 0 24 24" style="margin-right: 8px;">
                            <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                            <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                            <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                            <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                        </svg>
                        Continue with Google
                    </button>

                    <div class="divider">
                        <span>or</span>
                    </div>

                    <form id="auth-form">
                        <div class="form-group">
                            <input type="email" id="email" class="form-input" placeholder="Email" required>
                        </div>
                        <div class="form-group">
                            <input type="password" id="password" class="form-input" placeholder="Password" required>
                        </div>
                        <button type="submit" class="btn btn-primary" style="width: 100%;">
                            Sign In
                        </button>
                    </form>
                    <p style="text-align: center; margin-top: 16px;">
                        Don't have an account? 
                        <button onclick="toggleAuthMode()" style="background: none; border: none; color: #00bfff; cursor: pointer; text-decoration: underline;">
                            Sign Up
                        </button>
                    </p>
                </div>
            </div>
            <button onclick="showWelcome()" class="btn" style="margin-top: 16px;">
                ← Back to Welcome
            </button>
            <div id="auth-status"></div>
        </div>

        <!-- OTP Verification Screen -->
        <div id="otp-screen" class="welcome-content hidden">
            <h1 class="welcome-title">📧 Verify Your Email</h1>
            <div class="card">
                <div class="auth-form">
                    <h2>Enter Verification Code</h2>
                    <p style="color: var(--text-secondary); margin-bottom: 1rem;">
                        We've sent a 6-digit code to: <br>
                        <strong id="verification-email" style="color: var(--accent-color);"></strong>
                    </p>

                    <div class="form-group">
                        <input type="text" id="otp-input" class="form-input" placeholder="Enter 6-digit code" maxlength="6" style="text-align: center; font-size: 1.5rem; letter-spacing: 0.5rem;">
                    </div>

                    <button onclick="verifyOTP()" class="btn btn-primary" style="width: 100%;" id="verify-btn">
                        ✅ Verify Code
                    </button>

                    <div style="text-align: center; margin-top: 1rem;">
                        <p style="color: var(--text-secondary); margin-bottom: 0.5rem;">Didn't receive the code?</p>
                        <button onclick="resendOTP()" class="btn btn-secondary" id="resend-btn">
                            📤 Resend Code
                        </button>
                    </div>
                </div>
            </div>
            <button onclick="showAuth()" class="btn" style="margin-top: 16px;">
                ← Back to Sign Up
            </button>
            <div id="otp-status"></div>
        </div>


    </div>

    <script>
        // Supabase configuration
        const supabaseUrl = 'https://ogjjhakrquzhgtbxonjr.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9nampoYWtycXV6aGd0YnhvbmpyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTk0OTUwNTUsImV4cCI6MjA3NTA3MTA1NX0.C5fsoYz8lvYuMTVn_8oodRMjKt6bKUsYmxn_gy4dMc8';
        
        let supabase;
        let currentUser = null;
        let isSignUpMode = false;
        let pendingEmail = null;

        // Initialize Supabase
        try {
            supabase = window.supabase.createClient(supabaseUrl, supabaseKey);
            console.log('Supabase client initialized successfully');
        } catch (error) {
            console.error('Error initializing Supabase:', error);
            showStatus('Error connecting to Supabase', 'error');
        }

        // Screen management
        function showScreen(screenId) {
            document.querySelectorAll('[id$="-screen"]').forEach(screen => {
                screen.classList.add('hidden');
            });
            document.getElementById(screenId).classList.remove('hidden');
        }

        function showWelcome() {
            showScreen('welcome-screen');
        }

        function showAuth() {
            showScreen('auth-screen');
        }

        function showDemo() {
            // Redirect directly to NASA Event Location Gateway
            window.location.href = 'Event location nasa/index.html';
        }

        function showOTPVerification(email) {
            pendingEmail = email;
            document.getElementById('verification-email').textContent = email;
            showScreen('otp-screen');

            // Clear previous OTP input
            document.getElementById('otp-input').value = '';

            showOTPStatus('📧 Verification code sent to your email. Please check your inbox.', 'success');
        }

        async function verifyOTP() {
            const otpCode = document.getElementById('otp-input').value.trim();

            if (!otpCode || otpCode.length !== 6) {
                showOTPStatus('Please enter a valid 6-digit code.', 'error');
                return;
            }

            const verifyBtn = document.getElementById('verify-btn');
            verifyBtn.disabled = true;
            verifyBtn.textContent = '🔄 Verifying...';

            try {
                const { data, error } = await supabase.auth.verifyOtp({
                    email: pendingEmail,
                    token: otpCode,
                    type: 'signup'
                });

                if (error) {
                    showOTPStatus(`Verification failed: ${error.message}`, 'error');
                    verifyBtn.disabled = false;
                    verifyBtn.textContent = '✅ Verify Code';
                } else {
                    showOTPStatus('✅ Email verified successfully! Redirecting to NASA Event Location Gateway...', 'success');
                    currentUser = data.user;
                    setTimeout(() => {
                        window.location.href = 'Event location nasa/index.html';
                    }, 1500);
                }
            } catch (error) {
                showOTPStatus(`Verification error: ${error.message}`, 'error');
                verifyBtn.disabled = false;
                verifyBtn.textContent = '✅ Verify Code';
            }
        }

        async function resendOTP() {
            if (!pendingEmail) {
                showOTPStatus('No email address found. Please go back and sign up again.', 'error');
                return;
            }

            const resendBtn = document.getElementById('resend-btn');
            resendBtn.disabled = true;
            resendBtn.textContent = '📤 Sending...';

            try {
                const { error } = await supabase.auth.resend({
                    type: 'signup',
                    email: pendingEmail
                });

                if (error) {
                    showOTPStatus(`Failed to resend code: ${error.message}`, 'error');
                } else {
                    showOTPStatus('📧 New verification code sent! Please check your email.', 'success');
                }
            } catch (error) {
                showOTPStatus(`Resend error: ${error.message}`, 'error');
            }

            resendBtn.disabled = false;
            resendBtn.textContent = '📤 Resend Code';
        }

        function showOTPStatus(message, type) {
            const statusDiv = document.getElementById('otp-status');
            statusDiv.innerHTML = `<div class="status-message ${type}">${message}</div>`;
        }

        async function signInWithGoogle() {
            try {
                showStatus('🔄 Connecting to Google...', 'info');

                const { data, error } = await supabase.auth.signInWithOAuth({
                    provider: 'google',
                    options: {
                        redirectTo: 'http://localhost:3000/home.html',
                        queryParams: {
                            access_type: 'offline',
                            prompt: 'consent',
                        }
                    }
                });

                if (error) {
                    showStatus(`Google sign-in error: ${error.message}`, 'error');
                } else {
                    // The redirect will happen automatically
                    showStatus('🚀 Redirecting to Google...', 'success');
                }
            } catch (error) {
                showStatus(`Connection error: ${error.message}`, 'error');
            }
        }

        // Auth functions
        function toggleAuthMode() {
            isSignUpMode = !isSignUpMode;
            const title = document.getElementById('auth-title');
            const submitBtn = document.querySelector('#auth-form button[type="submit"]');
            const toggleText = document.querySelector('button[onclick="toggleAuthMode()"]').previousSibling;
            
            if (isSignUpMode) {
                title.textContent = 'Sign Up for Nimbus';
                submitBtn.textContent = 'Sign Up';
                toggleText.textContent = 'Already have an account? ';
                document.querySelector('button[onclick="toggleAuthMode()"]').textContent = 'Sign In';
            } else {
                title.textContent = 'Sign In to Nimbus';
                submitBtn.textContent = 'Sign In';
                toggleText.textContent = "Don't have an account? ";
                document.querySelector('button[onclick="toggleAuthMode()"]').textContent = 'Sign Up';
            }
        }

        function showStatus(message, type = 'success') {
            const statusDiv = document.getElementById('auth-status');
            statusDiv.innerHTML = `<div class="status-message status-${type}">${message}</div>`;
            setTimeout(() => {
                statusDiv.innerHTML = '';
            }, 5000);
        }

        async function handleAuth(email, password) {
            try {
                if (isSignUpMode) {
                    const { data, error } = await supabase.auth.signUp({
                        email: email,
                        password: password,
                        options: {
                            emailRedirectTo: window.location.origin + '/home.html',
                            data: {
                                full_name: 'User'
                            }
                        }
                    });
                    
                    if (error) {
                        showStatus(error.message, 'error');
                    } else {
                        if (data.user && !data.user.email_confirmed_at) {
                            // Show OTP verification form
                            showOTPVerification(email);
                        } else {
                            showStatus('✅ Account created successfully! Redirecting to NASA Event Location Gateway...', 'success');
                            currentUser = data.user;
                            setTimeout(() => {
                                window.location.href = 'Event location nasa/index.html';
                            }, 1500);
                        }
                    }
                } else {
                    const { data, error } = await supabase.auth.signInWithPassword({
                        email: email,
                        password: password,
                    });
                    
                    if (error) {
                        showStatus(error.message, 'error');
                    } else {
                        currentUser = data.user;
                        showStatus('Login successful! Redirecting to NASA Event Location Gateway...', 'success');
                        setTimeout(() => {
                            window.location.href = 'Event location nasa/index.html';
                        }, 1500);
                    }
                }
            } catch (error) {
                showStatus('Connection error: ' + error.message, 'error');
            }
        }

        async function signOut() {
            try {
                const { error } = await supabase.auth.signOut();
                if (error) {
                    showStatus(error.message, 'error');
                } else {
                    currentUser = null;
                    showWelcome();
                }
            } catch (error) {
                showStatus('Error signing out: ' + error.message, 'error');
            }
        }



        // Event listeners
        document.getElementById('auth-form').addEventListener('submit', function(e) {
            e.preventDefault();
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            handleAuth(email, password);
        });

        // Always show authentication page first - clear any existing sessions
        window.addEventListener('load', async function() {
            try {
                // Clear any existing session to force fresh login
                await supabase.auth.signOut();
                currentUser = null;
                showAuth(); // Show authentication form directly
            } catch (error) {
                console.error('Error clearing session:', error);
                showAuth(); // Show authentication form directly
            }

            // Add OTP input event listener
            const otpInput = document.getElementById('otp-input');
            if (otpInput) {
                otpInput.addEventListener('input', function(e) {
                    // Only allow numbers
                    e.target.value = e.target.value.replace(/[^0-9]/g, '');

                    // Auto-verify when 6 digits are entered
                    if (e.target.value.length === 6) {
                        setTimeout(() => {
                            verifyOTP();
                        }, 500);
                    }
                });
            }
        });

        // Listen for auth state changes
        if (supabase) {
            supabase.auth.onAuthStateChange((event, session) => {
                if (event === 'SIGNED_IN' && session?.user) {
                    currentUser = session.user;
                    // Don't automatically redirect - let manual authentication handle it
                } else if (event === 'SIGNED_OUT') {
                    currentUser = null;
                    showAuth(); // Show authentication form when signed out
                }
            });
        }
    </script>
</body>
</html>
