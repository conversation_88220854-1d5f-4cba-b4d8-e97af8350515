<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Confirm Email - Nimbus Admin</title>
    <style>
        body {
            font-family: 'Orbitron', monospace;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            color: #fff;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
        }
        .container {
            background: rgba(0, 191, 255, 0.1);
            border: 1px solid #00bfff;
            border-radius: 16px;
            padding: 2rem;
            max-width: 600px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        .title {
            color: #00bfff;
            margin-bottom: 1rem;
            text-shadow: 0 0 10px #00bfff;
        }
        .btn {
            background: linear-gradient(45deg, #00bfff, #0080ff);
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            color: white;
            cursor: pointer;
            margin: 8px;
            font-size: 16px;
            width: 100%;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 191, 255, 0.4);
        }
        .result {
            background: rgba(57, 255, 20, 0.1);
            border: 1px solid #39ff14;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            font-family: monospace;
        }
        .error {
            background: rgba(255, 71, 87, 0.1);
            border-color: #ff4757;
            color: #ff4757;
        }
        .warning {
            background: rgba(255, 170, 0, 0.1);
            border: 1px solid #ffaa00;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            color: #ffaa00;
        }
        input {
            width: 100%;
            padding: 12px;
            margin: 8px 0;
            background: rgba(0, 191, 255, 0.1);
            border: 1px solid #00bfff;
            border-radius: 8px;
            color: white;
            font-size: 16px;
        }
        input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }
        .code {
            background: rgba(0, 0, 0, 0.3);
            padding: 0.5rem;
            border-radius: 4px;
            font-family: monospace;
            margin: 0.5rem 0;
            word-break: break-all;
        }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&display=swap" rel="stylesheet">
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body>
    <div class="container">
        <h1 class="title">📧 Email Confirmation Tool</h1>
        
        <div class="warning">
            <h3>⚠️ Admin Tool</h3>
            <p>This tool uses SERVICE ROLE KEY to manually confirm email addresses for development/testing.</p>
        </div>

        <p>Enter your User ID to confirm your email and enable login:</p>
        
        <input type="text" id="userId" placeholder="Enter your User ID" value="a265ab37-5663-45d8-8d69-8cae7a8f12b9">
        
        <button class="btn" onclick="confirmEmail()">Confirm Email</button>
        
        <div id="result"></div>

        <div style="text-align: left; margin-top: 2rem;">
            <h3>📋 Instructions:</h3>
            <ol>
                <li>Your User ID is already filled in: <div class="code">a265ab37-5663-45d8-8d69-8cae7a8f12b9</div></li>
                <li>Click "Confirm Email" to manually verify your account</li>
                <li>After confirmation, go back to <a href="nimbus.html" style="color: #00bfff;">nimbus.html</a> and login</li>
                <li>You'll be taken to your dashboard automatically</li>
            </ol>
        </div>
    </div>

    <script>
        // Supabase configuration with SERVICE ROLE KEY
        const supabaseUrl = 'https://ogjjhakrquzhgtbxonjr.supabase.co';
        const serviceRoleKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.4mOuzlZJxNgsWrrQEdV_0aoJaAL-w2E0aZ9MjbjslRM';
        
        const supabaseAdmin = window.supabase.createClient(supabaseUrl, serviceRoleKey);

        function showResult(content, isError = false) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `<div class="result ${isError ? 'error' : ''}">${content}</div>`;
        }

        async function confirmEmail() {
            const userId = document.getElementById('userId').value.trim();
            
            if (!userId) {
                showResult('Please enter a User ID.', true);
                return;
            }

            try {
                showResult('Confirming email...');
                
                // Update user to confirm email using admin client
                const { data, error } = await supabaseAdmin.auth.admin.updateUserById(userId, {
                    email_confirm: true
                });

                if (error) {
                    showResult(`Error confirming email: ${error.message}`, true);
                    return;
                }

                showResult(`✅ Email confirmed successfully!

User ID: ${userId}
Status: Email verified
Action: You can now login

🎉 Next Steps:
1. Go to nimbus.html
2. Login with your email and password
3. You'll be taken to your dashboard automatically

<a href="nimbus.html" style="color: #00bfff; text-decoration: underline;">Click here to go to login page</a>`);

            } catch (error) {
                showResult(`Connection error: ${error.message}`, true);
            }
        }

        // Show initial message
        showResult(`🔧 Email Confirmation Tool

Your User ID is already filled in. Click "Confirm Email" to manually verify your account and enable login.

This bypasses the email confirmation requirement for development/testing.`);
    </script>
</body>
</html>
