<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#1a1a2e" />
    <title>Nimbus - Cyberpunk Event Planner</title>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- React and ReactDOM from CDN -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    
    <style>
        /* CSS Variables */
        :root {
            --primary-bg: #1a1a2e;
            --secondary-bg: #16213e;
            --accent-color: #00bfff;
            --success-color: #39ff14;
            --warning-color: #ffaa00;
            --error-color: #ff4757;
            --text-primary: #ffffff;
            --text-secondary: #b8c5d6;
            --text-muted: #8892b0;
            --border-color: #2d3748;
            --shadow-color: rgba(0, 191, 255, 0.3);
            --glow-color: rgba(0, 191, 255, 0.6);
            
            /* Typography */
            --font-heading: 'Orbitron', monospace;
            --font-body: 'Rajdhani', sans-serif;
            
            /* Spacing */
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --spacing-xl: 2rem;
            --spacing-2xl: 3rem;
            
            /* Border radius */
            --radius-sm: 4px;
            --radius-md: 8px;
            --radius-lg: 12px;
            --radius-xl: 16px;
            
            /* Transitions */
            --transition-fast: 0.15s ease;
            --transition-normal: 0.3s ease;
            --transition-slow: 0.5s ease;
        }

        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-body);
            background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 100%);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes glowPulse {
            0%, 100% { box-shadow: 0 0 5px var(--glow-color); }
            50% { box-shadow: 0 0 20px var(--glow-color), 0 0 30px var(--glow-color); }
        }

        /* Button styles */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-sm) var(--spacing-lg);
            border: 2px solid var(--accent-color);
            background: transparent;
            color: var(--accent-color);
            font-family: var(--font-body);
            font-weight: 600;
            font-size: 1rem;
            text-decoration: none;
            border-radius: var(--radius-md);
            cursor: pointer;
            transition: all var(--transition-normal);
            position: relative;
            overflow: hidden;
        }

        .btn:hover {
            background: var(--accent-color);
            color: var(--primary-bg);
            box-shadow: 0 0 20px var(--glow-color);
            transform: translateY(-2px);
        }

        .btn-primary {
            background: var(--accent-color);
            color: var(--primary-bg);
        }

        .btn-primary:hover {
            background: transparent;
            color: var(--accent-color);
        }

        /* Card styles */
        .card {
            background: rgba(22, 33, 62, 0.8);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            transition: all var(--transition-normal);
        }

        .card:hover {
            border-color: var(--accent-color);
            box-shadow: 0 8px 32px rgba(0, 191, 255, 0.2);
        }

        /* Layout utilities */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--spacing-lg);
        }

        /* Typography */
        h1, h2, h3, h4, h5, h6 {
            font-family: var(--font-heading);
            font-weight: 700;
            margin-bottom: var(--spacing-md);
        }

        h1 {
            font-size: 3rem;
            background: linear-gradient(45deg, var(--accent-color), var(--success-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        h2 {
            font-size: 2rem;
            color: var(--accent-color);
        }

        /* App specific styles */
        .app {
            min-height: 100vh;
            animation: fadeIn 1s ease-out;
        }

        .welcome-screen {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            text-align: center;
            padding: var(--spacing-xl);
        }

        .welcome-title {
            font-size: 4rem;
            margin-bottom: var(--spacing-lg);
            animation: glowPulse 2s infinite;
        }

        .welcome-subtitle {
            font-size: 1.5rem;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-2xl);
            max-width: 600px;
        }

        .auth-buttons {
            display: flex;
            gap: var(--spacing-lg);
            flex-wrap: wrap;
            justify-content: center;
        }

        .demo-info {
            margin-top: var(--spacing-2xl);
            padding: var(--spacing-lg);
            background: rgba(0, 191, 255, 0.1);
            border: 1px solid var(--accent-color);
            border-radius: var(--radius-lg);
            max-width: 500px;
        }

        .demo-info h3 {
            color: var(--accent-color);
            margin-bottom: var(--spacing-sm);
        }

        .demo-info p {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .grid {
            display: grid;
            gap: var(--spacing-lg);
        }

        .grid-2 {
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        }

        .grid-3 {
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        }

        .text-center {
            text-align: center;
        }

        .mb-2 {
            margin-bottom: var(--spacing-lg);
        }

        .mt-2 {
            margin-top: var(--spacing-lg);
        }

        .flex {
            display: flex;
        }

        .flex-col {
            flex-direction: column;
        }

        .justify-between {
            justify-content: space-between;
        }

        .items-center {
            align-items: center;
        }

        .gap-1 {
            gap: var(--spacing-sm);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: var(--spacing-xs);
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .alert {
            padding: var(--spacing-sm);
            border-radius: var(--radius-sm);
            margin-bottom: var(--spacing-sm);
        }

        .alert-info {
            background: rgba(0, 191, 255, 0.1);
            border-left: 3px solid var(--accent-color);
        }

        .alert-success {
            background: rgba(57, 255, 20, 0.1);
            border-left: 3px solid var(--success-color);
        }

        .event-item {
            border-left: 3px solid var(--accent-color);
            padding-left: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .event-item.success {
            border-left-color: var(--success-color);
        }

        .event-title {
            font-weight: bold;
            margin-bottom: var(--spacing-xs);
        }

        .event-time {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .welcome-title {
                font-size: 2.5rem;
            }
            
            .auth-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .container {
                padding: 0 var(--spacing-md);
            }
        }
    </style>
</head>
<body>
    <div id="root"></div>
    
    <script type="text/babel">
        const { useState, useEffect } = React;

        // Supabase configuration
        const supabaseUrl = 'https://ogjjhakrquzhgtbxonjr.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9nampoYWtycXV6aGd0YnhvbmpyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTk0OTUwNTUsImV4cCI6MjA3NTA3MTA1NX0.C5fsoYz8lvYuMTVn_8oodRMjKt6bKUsYmxn_gy4dMc8';
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);
        
        function App() {
            const [currentView, setCurrentView] = useState('welcome');
            const [user, setUser] = useState(null);
            const [loading, setLoading] = useState(true);

            // Check for existing session on load
            useEffect(() => {
                supabase.auth.getSession().then(({ data: { session } }) => {
                    setUser(session?.user ?? null);
                    setLoading(false);
                });

                const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
                    setUser(session?.user ?? null);
                });

                return () => subscription.unsubscribe();
            }, []);

            // Authentication functions
            const signUp = async (email, password) => {
                const { data, error } = await supabase.auth.signUp({
                    email: email,
                    password: password,
                });
                if (error) alert(error.message);
                else alert('Check your email for verification link!');
            };

            const signIn = async (email, password) => {
                const { data, error } = await supabase.auth.signInWithPassword({
                    email: email,
                    password: password,
                });
                if (error) alert(error.message);
                else setCurrentView('dashboard');
            };

            const signOut = async () => {
                const { error } = await supabase.auth.signOut();
                if (error) alert(error.message);
                else setCurrentView('welcome');
            };
            const [user, setUser] = useState(null);
            
            const handleSignUp = () => {
                setUser({ name: 'Demo User', email: '<EMAIL>' });
                setCurrentView('dashboard');
            };
            
            const handleLogin = () => {
                setUser({ name: 'Demo User', email: '<EMAIL>' });
                setCurrentView('dashboard');
            };

            // Login form component
            const LoginForm = ({ onSignIn, onSignUp }) => {
                const [email, setEmail] = useState('');
                const [password, setPassword] = useState('');
                const [isSignUp, setIsSignUp] = useState(false);

                const handleSubmit = (e) => {
                    e.preventDefault();
                    if (isSignUp) {
                        onSignUp(email, password);
                    } else {
                        onSignIn(email, password);
                    }
                };

                return (
                    <div className="auth-form">
                        <h2>{isSignUp ? 'Sign Up' : 'Sign In'} to Nimbus</h2>
                        <form onSubmit={handleSubmit}>
                            <input
                                type="email"
                                placeholder="Email"
                                value={email}
                                onChange={(e) => setEmail(e.target.value)}
                                required
                                style={{
                                    width: '100%',
                                    padding: '12px',
                                    margin: '8px 0',
                                    background: 'rgba(0, 191, 255, 0.1)',
                                    border: '1px solid #00bfff',
                                    borderRadius: '8px',
                                    color: '#fff',
                                    fontSize: '16px'
                                }}
                            />
                            <input
                                type="password"
                                placeholder="Password"
                                value={password}
                                onChange={(e) => setPassword(e.target.value)}
                                required
                                style={{
                                    width: '100%',
                                    padding: '12px',
                                    margin: '8px 0',
                                    background: 'rgba(0, 191, 255, 0.1)',
                                    border: '1px solid #00bfff',
                                    borderRadius: '8px',
                                    color: '#fff',
                                    fontSize: '16px'
                                }}
                            />
                            <button type="submit" className="cta-button">
                                {isSignUp ? 'Sign Up' : 'Sign In'}
                            </button>
                        </form>
                        <p style={{ textAlign: 'center', marginTop: '16px' }}>
                            {isSignUp ? 'Already have an account?' : "Don't have an account?"}{' '}
                            <button
                                onClick={() => setIsSignUp(!isSignUp)}
                                style={{
                                    background: 'none',
                                    border: 'none',
                                    color: '#00bfff',
                                    cursor: 'pointer',
                                    textDecoration: 'underline'
                                }}
                            >
                                {isSignUp ? 'Sign In' : 'Sign Up'}
                            </button>
                        </p>
                    </div>
                );
            };

            if (loading) {
                return (
                    <div className="container">
                        <div className="welcome-content">
                            <h1 className="welcome-title">NIMBUS</h1>
                            <p>Loading...</p>
                        </div>
                    </div>
                );
            }

            if (currentView === 'welcome') {
                return (
                    <div className="app">
                        <div className="welcome-screen">
                            <h1 className="welcome-title">NIMBUS</h1>
                            <p className="welcome-subtitle">
                                Plan your events with futuristic style. Experience the power of cyberpunk aesthetics 
                                combined with intelligent event management and personalization.
                            </p>
                            
                            <div className="auth-buttons">
                                <button className="btn btn-primary" onClick={() => setCurrentView('auth')}>
                                    🚀 Get Started
                                </button>
                                <button className="btn" onClick={() => setCurrentView('dashboard')}>
                                    🎮 View Demo
                                </button>
                            </div>
                            
                            <div className="demo-info">
                                <h3>🎮 Interactive Demo</h3>
                                <p>
                                    Welcome to Nimbus - your cyberpunk event planning platform! Click any button above to explore
                                    the futuristic interface. This version includes full Supabase integration,
                                    real-time updates, weather alerts, and complete event management.
                                </p>
                            </div>
                        </div>
                    </div>
                );
            }

            if (currentView === 'auth') {
                return (
                    <div className="container">
                        <div className="welcome-content">
                            <h1 className="welcome-title">NIMBUS</h1>
                            <LoginForm onSignIn={signIn} onSignUp={signUp} />
                            <button
                                onClick={() => setCurrentView('welcome')}
                                style={{
                                    background: 'none',
                                    border: '1px solid #00bfff',
                                    color: '#00bfff',
                                    padding: '8px 16px',
                                    borderRadius: '8px',
                                    cursor: 'pointer',
                                    marginTop: '16px'
                                }}
                            >
                                ← Back to Welcome
                            </button>
                        </div>
                    </div>
                );
            }

            if (currentView === 'dashboard') {
                return (
                    <div className="app">
                        <div className="container" style={{ paddingTop: '2rem' }}>
                            <div className="flex justify-between items-center mb-2">
                                <h1>Welcome to Nimbus, {user ? user.email : 'User'}! 🌟</h1>
                                <button className="btn" onClick={signOut}>
                                    ⚡ Logout
                                </button>
                            </div>
                            
                            <div className="grid grid-2">
                                <div className="card">
                                    <h2>📊 Your Stats</h2>
                                    <div className="grid" style={{ gridTemplateColumns: 'repeat(2, 1fr)', marginTop: '1rem' }}>
                                        <div className="text-center">
                                            <div className="stat-number" style={{ color: 'var(--accent-color)' }}>12</div>
                                            <div className="stat-label">Events Created</div>
                                        </div>
                                        <div className="text-center">
                                            <div className="stat-number" style={{ color: 'var(--success-color)' }}>8</div>
                                            <div className="stat-label">Events Attended</div>
                                        </div>
                                        <div className="text-center">
                                            <div className="stat-number" style={{ color: 'var(--warning-color)' }}>5</div>
                                            <div className="stat-label">Saved Events</div>
                                        </div>
                                        <div className="text-center">
                                            <div className="stat-number" style={{ color: 'var(--error-color)' }}>3</div>
                                            <div className="stat-label">Active Alerts</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div className="card">
                                    <h2>🎯 Quick Actions</h2>
                                    <div className="flex flex-col gap-1 mt-2">
                                        <button className="btn">🎪 Create New Event</button>
                                        <button className="btn">🔍 Browse Events</button>
                                        <button className="btn">⚙️ Update Preferences</button>
                                        <button className="btn">🌦️ Weather Alerts</button>
                                    </div>
                                </div>
                                
                                <div className="card">
                                    <h2>🔔 Recent Alerts</h2>
                                    <div className="mt-2">
                                        <div className="alert alert-info">
                                            <div style={{ fontWeight: 'bold', color: 'var(--accent-color)' }}>🌧️ Weather Alert</div>
                                            <div style={{ fontSize: '0.9rem', color: 'var(--text-secondary)' }}>Rain expected for your outdoor event tomorrow</div>
                                        </div>
                                        <div className="alert alert-success">
                                            <div style={{ fontWeight: 'bold', color: 'var(--success-color)' }}>⏰ Event Reminder</div>
                                            <div style={{ fontSize: '0.9rem', color: 'var(--text-secondary)' }}>Cyberpunk Conference starts in 2 hours</div>
                                        </div>
                                        <div className="alert alert-info">
                                            <div style={{ fontWeight: 'bold', color: 'var(--accent-color)' }}>🎮 New Recommendation</div>
                                            <div style={{ fontSize: '0.9rem', color: 'var(--text-secondary)' }}>Gaming meetup matches your interests</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div className="card">
                                    <h2>🎮 Plan Your Event</h2>
                                    <div className="mt-2">
                                        <div className="event-item">
                                            <div className="event-title">🎯 Cyberpunk 2077 Meetup</div>
                                            <div className="event-time">Tomorrow, 7:00 PM • Gaming</div>
                                        </div>
                                        <div className="event-item success">
                                            <div className="event-title">⚡ Neon Gaming Tournament</div>
                                            <div className="event-time">Friday, 6:00 PM • Competition</div>
                                        </div>
                                        <div className="event-item">
                                            <div className="event-title">🌃 Night Photography Walk</div>
                                            <div className="event-time">Saturday, 9:00 PM • Photography</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div className="demo-info mt-2" style={{ maxWidth: 'none' }}>
                                <h3>🎉 Cyberpunk Event Planner Features</h3>
                                <div className="grid grid-3 mt-2">
                                    <div>
                                        <strong>✨ Profile Management:</strong> Customize your cyberpunk persona with preferences and activity tracking
                                    </div>
                                    <div>
                                        <strong>🎯 Smart Recommendations:</strong> AI-powered event suggestions based on your interests and behavior
                                    </div>
                                    <div>
                                        <strong>🌦️ Weather Integration:</strong> Real-time weather alerts and recommendations for your events
                                    </div>
                                    <div>
                                        <strong>🔔 Personalized Alerts:</strong> Custom notifications for events, weather, and important updates
                                    </div>
                                    <div>
                                        <strong>🎮 Event Discovery:</strong> Find and create events that match your cyberpunk lifestyle
                                    </div>
                                    <div>
                                        <strong>📱 Real-time Updates:</strong> Live notifications and seamless synchronization across devices
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                );
            }
            
            return null;
        }
        
        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
