<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Location Gateway - Member 1</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary-dark': '#10142C',
                        'accent-cyan': '#00FFFF',
                        'accent-purple': '#8A2BE2',
                        'module-bg': '#191C3E',
                        'border-color': '#2C3E50',
                    },
                    fontFamily: {
                        sans: ['Inter', 'sans-serif'],
                    },
                    boxShadow: {
                        'neon': '0 0 15px rgba(0, 255, 255, 0.7)',
                        'input-glow': '0 0 5px rgba(0, 255, 255, 0.5)',
                        'inner-grid': 'inset 0 0 5px rgba(138, 43, 226, 0.5)',
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #10142C;
            background-image: url('https://placehold.co/1200x800/10142C/191C3E?text=SCI-FI+INTERFACE');
            background-size: cover;
            background-blend-mode: multiply;
            min-height: 100vh;
        }

        .globe-icon {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 200px;
            color: rgba(0, 255, 255, 0.1); 
            pointer-events: none;
            text-shadow: 0 0 50px rgba(0, 255, 255, 0.3);
            animation: pulse 5s infinite alternate;
        }

        @keyframes pulse {
            0% { transform: translate(-50%, -50%) scale(1); opacity: 0.1; }
            100% { transform: translate(-50%, -50%) scale(1.05); opacity: 0.18; }
        }
        
        #location-query:focus {
            border-color: #00FFFF !important;
            box-shadow: 0 0 8px rgba(0, 255, 255, 0.8) !important;
        }

        #use-gps-button {
            background-color: #8A2BE2; 
            box-shadow: 0 0 10px #00FFFF;
            transition: all 0.3s ease;
        }

        #use-gps-button:hover {
            box-shadow: 0 0 25px #00FFFF, 0 0 5px #8A2BE2;
            background-color: #9370DB; 
        }
        
        .spinner {
            border: 4px solid rgba(255, 255, 255, 0.1);
            border-top: 4px solid #00FFFF; 
            border-radius: 50%;
            width: 1.5rem;
            height: 1.5rem;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="text-gray-100 p-4 flex items-center justify-center">

    <div class="globe-icon">🌐</div>

    <div class="w-full max-w-xl p-6 bg-module-bg/90 backdrop-blur-sm rounded-xl shadow-2xl z-10 border border-accent-cyan/30">
        <h1 class="text-3xl font-bold mb-6 text-accent-cyan text-center tracking-wider" style="text-shadow: 0 0 5px #00FFFF;">
            EVENT LOCATION GATEWAY
        </h1>

        <form id="location-form" class="space-y-6">
            
            <h2 class="text-xl font-semibold text-gray-200 border-b border-border-color pb-2">Enter Event Location</h2>

            <div class="relative">
                <label for="location-query" class="text-sm font-medium text-gray-400 mb-1 block">Location Search (City, Pincode, Address)</label>
                <div class="flex">
                    <input 
                        type="text" 
                        id="location-query" 
                        placeholder="Enter city, place, or pin code..." 
                        class="flex-grow p-3 bg-primary-dark border border-border-color rounded-l-lg text-white placeholder-gray-500 transition duration-200 focus:outline-none" 
                        required
                    >
                    <button type="submit" id="search-button" class="p-3 bg-accent-purple hover:bg-accent-purple/80 text-white font-semibold rounded-r-lg transition duration-200 flex items-center justify-center">
                        <span id="search-text">Search</span>
                        <div id="search-spinner" class="spinner ml-2 hidden"></div>
                    </button>
                </div>
            </div>

            <div class="text-center text-gray-500 font-medium">--- LOCATOR MODE ---</div>

            <button type="button" id="use-gps-button" class="w-full py-3 text-white font-bold rounded-lg focus:outline-none transition duration-300 flex items-center justify-center" disabled>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.828 0l-4.243-4.243m4.243 0a2.002 2.002 0 00.493-1.743l1.848-7.915a1 1 0 011.979 0l1.848 7.915a2.002 2.002 0 00.493 1.743z" />
                </svg>
                <span id="gps-text">Use My Current Location</span>
                <div id="gps-spinner" class="spinner ml-2 hidden"></div>
            </button>

            <div id="location-preview-area" class="p-4 bg-primary-dark rounded-lg border border-accent-cyan/50 shadow-inner-grid">
                <h3 class="text-lg font-medium text-accent-cyan mb-2">Location Data:</h3>
                
                <div id="custom-name-group" class="mb-3 hidden">
                    <label for="custom-location-name" class="text-xs font-medium text-gray-400 mb-1 block">Rename/Confirm Location:</label>
                    <input type="text" id="custom-location-name" placeholder="E.g., Event Venue, Home" class="w-full p-2 bg-module-bg border border-border-color rounded-md text-white focus:border-accent-cyan">
                </div>
                
                <p>Name: <span id="location-name-display" class="font-bold text-white">Awaiting Input...</span></p>
                <p>Coordinates: <span id="lat-lon-display" class="font-mono text-sm text-gray-400">LAT: --, LON: --</span></p>
            </div>

            <div class="flex space-x-4">
                <button type="button" id="save-location-button" class="flex-1 py-2 button-theme transition duration-200" disabled>
                    💾 Save Location
                </button>
                <button type="button" id="view-saved-button" class="flex-1 py-2 button-theme transition duration-200">
                    📜 View Saved
                </button>
            </div>


            <div id="map-preview" class="h-40 bg-primary-dark border border-accent-cyan/50 rounded-lg flex items-center justify-center shadow-inner-grid text-gray-500">
                <p>Map Preview Area (Member 3 Visualization)</p>
            </div>

            <input type="hidden" id="lat-input" name="latitude">
            <input type="hidden" id="lon-input" name="longitude">
            <input type="hidden" id="name-input" name="location_name">
        </form>
    </div>

    <script>
        // === CRITICAL CONFIGURATION ===
        // 1. URL for your Python Flask backend (MUST be running: python app.py)
        const FLASK_URL = 'http://127.0.0.1:5000'; 
        // 2. Your Geoapify API Key (Used directly by the frontend for Reverse Geocoding)
        // You MUST replace this placeholder with your actual key (********************************)
        const GEOAPIFY_API_KEY = "********************************" ; 
        // 3. Geoapify Reverse Geocoding URL
        const REVERSE_GEOCODING_URL = 'https://api.geoapify.com/v1/geocode/reverse';

        // --- DOM Element Variables ---
        const form = document.getElementById('location-form');
        const queryInput = document.getElementById('location-query');
        const gpsButton = document.getElementById('use-gps-button');
        const searchButton = document.getElementById('search-button');
        const saveButton = document.getElementById('save-location-button');
        const latInput = document.getElementById('lat-input');
        const lonInput = document.getElementById('lon-input');
        const nameInput = document.getElementById('name-input');
        const nameDisplay = document.getElementById('location-name-display');
        const latLonDisplay = document.getElementById('lat-lon-display');
        const customNameGroup = document.getElementById('custom-name-group');
        const customNameInput = document.getElementById('custom-location-name');
        const searchSpinner = document.getElementById('search-spinner');
        const searchButtonText = document.getElementById('search-text');
        const gpsSpinner = document.getElementById('gps-spinner');
        const gpsButtonText = document.getElementById('gps-text');

        // --- Core Utility Functions ---

        function setLoading(isSearching, source = 'search') {
            searchButton.disabled = isSearching;
            gpsButton.disabled = isSearching;

            if (isSearching) {
                if (source === 'search') {
                    searchSpinner.classList.remove('hidden');
                    searchButtonText.textContent = '';
                } else if (source === 'gps') {
                    gpsSpinner.classList.remove('hidden');
                    gpsButtonText.textContent = 'Locating...';
                }
            } else {
                searchSpinner.classList.add('hidden');
                searchButtonText.textContent = 'Search';
                gpsSpinner.classList.add('hidden');
                gpsButtonText.textContent = 'Use My Current Location';
                if (navigator.geolocation) {
                    gpsButton.disabled = false;
                }
            }
        }
        
        function showMessage(type, message) {
            console.log(`[${type.toUpperCase()}] ${message}`);
            nameDisplay.textContent = type === 'error' ? 'ERROR!' : 'Success!';
            latLonDisplay.textContent = message;
            customNameGroup.classList.add('hidden');
        }

        // --- Helper Function to Update UI, Log Coordinates, and Clean Up (Task #4 & #5) ---
        function updateLocationDisplay(lat, lon, name) {
            // TASK #5: CLEAN UP / ROUNDING (Crucial for APIs)
            const cleanLat = parseFloat(lat).toFixed(6);
            const cleanLon = parseFloat(lon).toFixed(6);

            // 1. Update Hidden Form Fields
            latInput.value = cleanLat;
            lonInput.value = cleanLon;
            nameInput.value = name; // Uses the final, confirmed name

            // 2. Update Custom Name Input and Display
            customNameInput.value = name;
            nameDisplay.textContent = name;
            latLonDisplay.textContent = `LAT: ${cleanLat}, LON: ${cleanLon}`;
            
            // Show custom name input and enable save button
            customNameGroup.classList.remove('hidden');
            saveButton.disabled = false;
            
            // Event listener for custom name change
            customNameInput.oninput = () => {
                nameDisplay.textContent = customNameInput.value;
                nameInput.value = customNameInput.value; // Update hidden field instantly
            };

            // 3. Log to Console 
            console.log(`\n✅ LOCATION CAPTURED (Final): Name: ${name} -> Lat: ${cleanLat}, Lon: ${cleanLon}`);

            // 4. Placeholder for Member 3 Map Update (Visualization)
            const mapPlaceholder = document.getElementById('map-preview');
            mapPlaceholder.innerHTML = `<p class="text-accent-cyan font-bold">Location Selected: ${name}</p><p class="text-sm">Pin placed at: ${cleanLat}, ${cleanLon}</p>`;
            mapPlaceholder.classList.remove('text-gray-500');
            mapPlaceholder.classList.add('border-accent-cyan');
        }

        // --- NEW: Reverse Geocoding and Display Function ---
        async function reverseGeocodeAndDisplay(lat, lon) {
            if (!GEOAPIFY_API_KEY || GEOAPIFY_API_KEY === 'PASTE_YOUR_GEOAPIFY_KEY_HERE') {
                updateLocationDisplay(lat, lon, "Current GPS Location (KEY ERROR!)");
                showMessage('error', 'Reverse Geocoding failed: Geoapify API Key is missing in index.html.');
                return;
            }

            try {
                const response = await fetch(`${REVERSE_GEOCODING_URL}?lat=${lat}&lon=${lon}&apiKey=${GEOAPIFY_API_KEY}`);
                const data = await response.json();
                
                let name = "Custom GPS Location"; // Fallback name
                
                if (response.ok && data.features && data.features.length > 0) {
                    name = data.features[0].properties.formatted;
                } else if (!response.ok) {
                     // If Geoapify returns error, show a generic name but still use the coordinates
                     console.error("Geoapify Reverse Lookup Error:", data);
                     name = "Current GPS Location (Lookup Failed)";
                }
                
                updateLocationDisplay(lat, lon, name);

            } catch (error) {
                // Network failure
                updateLocationDisplay(lat, lon, "Current GPS Location (Network Error)");
            } finally {
                 setLoading(false); // Ensure loading state is reset
            }
        }


        // --- Search Bar Logic (Frontend Task #3: Talk to the Kitchen) ---
        form.addEventListener('submit', async (e) => {
            e.preventDefault(); 
            const query = queryInput.value.trim();
            if (!query) return;

            setLoading(true, 'search');
            nameDisplay.textContent = 'Searching...';
            latLonDisplay.textContent = 'Contacting Backend Translator...';
            saveButton.disabled = true;

            try {
                // Call the Flask backend geocoding endpoint
                const response = await fetch(`${FLASK_URL}/api/location/geocode?query=${encodeURIComponent(query)}`);
                const data = await response.json();

                if (response.ok) {
                    // Success: The Backend sent back the clean numbers and name
                    updateLocationDisplay(data.latitude, data.longitude, data.name);
                } else {
                    showMessage('error', `Search failed: ${data.error || 'Location not found.'}`);
                }
            } catch (error) {
                showMessage('error', `Network connection failed. Is the Flask server running at ${FLASK_URL}?`);
            } finally {
                setLoading(false, 'search');
            }
        });


        // --- GPS Button Logic (Frontend Task #2: Handle the GPS Button) ---
        gpsButton.addEventListener('click', () => {
            if (!navigator.geolocation) {
                showMessage('error', "Geolocation is not supported by your browser.");
                return;
            }

            setLoading(true, 'gps');
            nameDisplay.textContent = 'Requesting GPS signal...';
            latLonDisplay.textContent = 'Awaiting permission.';
            saveButton.disabled = true;

            navigator.geolocation.getCurrentPosition(
                (position) => {
                    const lat = position.coords.latitude;
                    const lon = position.coords.longitude;
                    
                    // NEW: Immediately initiate Reverse Geocoding to get the address name
                    reverseGeocodeAndDisplay(lat, lon); 
                },
                (error) => {
                    // Handle Geolocation errors (permission denied, timeout, etc.)
                    let message = "Could not get location. Check device settings.";
                    if (error.code === error.PERMISSION_DENIED) {
                        message = "Location access denied. Please enter manually.";
                    }
                    showMessage('error', message);
                    setLoading(false, 'gps');
                },
                { enableHighAccuracy: true, timeout: 7000, maximumAge: 0 } 
            );
        });

        // --- Day 2 Placeholder: Save Location Logic (Talks to Flask Memory) ---
        saveButton.addEventListener('click', async () => {
            const mockUserId = 'user_abc_123'; 
            const locationData = {
                user_id: mockUserId,
                location_name: nameInput.value,
                latitude: parseFloat(latInput.value),
                longitude: parseFloat(lonInput.value),
            };

            saveButton.textContent = 'Saving...';
            saveButton.disabled = true;

            try {
                const response = await fetch(`${FLASK_URL}/api/user/locations`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(locationData),
                });

                const data = await response.json();

                if (response.ok) {
                    showMessage('success', `Location saved to DB: ${locationData.location_name}`);
                } else {
                    showMessage('error', `Failed to save: ${data.error || 'Unknown error'}`);
                }

            } catch (error) {
                showMessage('error', `Network error while saving location.`);
            } finally {
                saveButton.textContent = '💾 Save Location';
                if (latInput.value && lonInput.value) {
                    saveButton.disabled = false;
                }
            }
        });
        
        // --- Initial Load Check for Geolocation Support ---
        window.onload = () => {
             if (navigator.geolocation) {
                gpsButton.disabled = false;
             }
        };

    </script>
</body>
</html>