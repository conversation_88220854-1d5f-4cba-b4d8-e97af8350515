# app.py

import os
import requests
import sqlite3
import json
from dotenv import load_dotenv
from flask import Flask, jsonify, request, g
from flask_cors import CORS 

# --- 1. SETUP & CONFIGURATION ---
load_dotenv() # Load environment variables from .env
app = Flask(__name__)
# Enable CORS for communication with the frontend
CORS(app) 
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'default-dev-key') 

# Geoapify Geocoding API configuration
GEOAPIFY_API_KEY = os.getenv('GEOAPIFY_API_KEY')
GEOAPIFY_BASE_URL = 'https://api.geoapify.com/v1/geocode/search' 

# Database configuration for reusable locations
DATABASE = 'locations.db'

# --- 2. DATABASE HELPER FUNCTIONS (SQLite) ---
def get_db():
    """Opens a new database connection for the current application context."""
    if 'db' not in g:
        g.db = sqlite3.connect(DATABASE)
        g.db.row_factory = sqlite3.Row 
    return g.db

@app.teardown_appcontext
def close_db(e=None):
    """Closes the database again at the end of the request."""
    db = g.pop('db', None)
    if db is not None:
        db.close()

# Initialize the database table on startup
with app.app_context():
    db = get_db()
    cursor = db.cursor()
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS locations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id TEXT,
            location_name TEXT NOT NULL,
            latitude REAL NOT NULL,
            longitude REAL NOT NULL,
            UNIQUE(user_id, location_name)
        )
    """)
    db.commit()
    
# --- 3. THE TRANSLATOR (Geocoding Endpoint: Supports the Search Bar) ---
@app.route('/api/location/geocode', methods=['GET'])
def geocode_location():
    """Converts a text query (city/address) into standardized lat/lon using Geoapify."""
    query = request.args.get('query')

    if not query:
        return jsonify({"error": "Missing required 'query' parameter."}), 400

    if not GEOAPIFY_API_KEY:
        print("!!! ERROR: GEOAPIFY_API_KEY is missing. Geocoding will fail. !!!")
        return jsonify({"error": "Server configuration error (Missing API Key)."}), 500

    try:
        # Call the Geoapify Geocoding API
        params = {
            'text': query,
            'apiKey': GEOAPIFY_API_KEY,
            'limit': 1, # Request only the top result
            'format': 'json'
        }
        response = requests.get(GEOAPIFY_BASE_URL, params=params)
        data = response.json()

        if response.ok and data.get('features'):
            # Geoapify returns results in GeoJSON format, nested under 'features'
            result_properties = data['features'][0]['properties']
            
            lat = result_properties['lat']
            lon = result_properties['lon']
            location_name = result_properties['formatted'] # The full address string

            location_data = {
                "latitude": lat,
                "longitude": lon,
                "name": location_name,
            }
            
            print(f"\n✅ Geoapify Success: {location_name} -> LAT={round(lat, 6)}, LON={round(lon, 6)}")
            
            return jsonify(location_data)

        elif response.ok and not data.get('features'):
            return jsonify({"error": "Location not found via Geoapify. Try a broader search."}), 404
        else:
            print(f"Geoapify API Error: {response.status_code}")
            return jsonify({"error": "External Geocoding Service Error. Check Geoapify usage/key."}), 500

    except Exception as e:
        print(f"Geocoding Request Failed: {e}")
        return jsonify({"error": "Server failed to connect to Geoapify API."}), 500

# --- 4. THE ROUTER (Central Gateway: Routes GPS/Search Coordinates to M2/M4) ---
@app.route('/api/event/location', methods=['GET'])
def route_location():
    """Accepts clean lat/lon and simulates passing it to other modules."""
    lat = request.args.get('lat')
    lon = request.args.get('lon')
    event = request.args.get('event', 'generic_event')

    if not lat or not lon:
        return jsonify({"error": "Missing required coordinates (lat, lon)."}), 400
    
    try:
        lat_f = float(lat)
        lon_f = float(lon)
        
        # --- LOCATION VALIDATION (Error Check for M2/M4) ---
        if abs(lat_f) > 85 or abs(lon_f) > 180:
            raise ValueError("LOCATION_UNSERVICEABLE")

        # --- SIMULATED CALLS TO TEAM MEMBERS ---
        print(f"\n➡️  Routing to Member 2 (Weather) for coordinates: {round(lat_f, 6)}, {round(lon_f, 6)}")
        print(f"➡️  Routing to Member 4 (Risk) for event: {event}")

        # If all checks and simulated routing succeed
        return jsonify({
            "status": "success",
            "message": "Location validated and routed successfully to all downstream systems.",
            "coordinates": {"lat": round(lat_f, 6), "lon": round(lon_f, 6)},
            "event": event,
        })

    except ValueError as e:
        if str(e) == "LOCATION_UNSERVICEABLE":
            return jsonify({
                "status": "error",
                "message": "Location is outside the serviceable area for our data sources (e.g., too far North/South).",
            }), 422
        return jsonify({"error": "Invalid numerical input for coordinates."}), 400
    
    except Exception as e:
        print(f"Internal Routing Error: {e}")
        return jsonify({"error": "Error during cross-module integration. Check server logs."}), 500


# --- 5. THE MEMORY (Reusable Locations Storage Endpoints) ---
@app.route('/api/user/locations', methods=['POST'])
def save_location():
    """Saves a user-defined location to the SQLite database."""
    data = request.json
    user_id = data.get('user_id')
    location_name = data.get('location_name')
    latitude = data.get('latitude')
    longitude = data.get('longitude')
    
    if not all([user_id, location_name, latitude is not None, longitude is not None]):
        return jsonify({"error": "Missing required fields for saving location."}), 400

    try:
        db = get_db()
        cursor = db.cursor()
        cursor.execute(
            """INSERT INTO locations (user_id, location_name, latitude, longitude) 
               VALUES (?, ?, ?, ?)""",
            (user_id, location_name, latitude, longitude)
        )
        db.commit()
        return jsonify({"status": "success", "id": cursor.lastrowid, "message": "Location saved."})
    
    except sqlite3.IntegrityError:
        return jsonify({"error": "This location name is already saved for this user."}), 409
    except Exception as e:
        return jsonify({"error": f"Database save error: {e}"}), 500

@app.route('/api/user/locations', methods=['GET'])
def get_locations():
    """Retrieves all saved locations for a specific user."""
    user_id = request.args.get('user_id')
    
    if not user_id:
        return jsonify({"error": "Missing required user_id parameter."}), 400

    try:
        db = get_db()
        cursor = db.cursor()
        cursor.execute(
            """SELECT location_name, latitude, longitude FROM locations WHERE user_id = ? ORDER BY location_name ASC""",
            (user_id,)
        )
        rows = [dict(row) for row in cursor.fetchall()]
        return jsonify({"locations": rows})
    
    except Exception as e:
        return jsonify({"error": f"Failed to retrieve locations: {e}"}), 500


# --- 6. START SERVER ---
if __name__ == '__main__':
    try:
        app.run(debug=True, port=5000) 
    except ImportError:
        print("\nERROR: Missing required Python libraries. Please install them by running: 'pip install Flask requests python-dotenv flask-cors'")