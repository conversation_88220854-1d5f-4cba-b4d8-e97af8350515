<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nimbus - Home Dashboard</title>
    <style>
        :root {
            --primary-bg: #1a1a2e;
            --secondary-bg: #16213e;
            --accent-color: #00bfff;
            --success-color: #39ff14;
            --warning-color: #ffaa00;
            --error-color: #ff4757;
            --text-primary: #ffffff;
            --text-secondary: #b8c5d6;
            --spacing-sm: 0.5rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --spacing-xl: 2rem;
            --spacing-2xl: 3rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Orbitron', 'Rajdhani', sans-serif;
            background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 100%);
            color: var(--text-primary);
            min-height: 100vh;
            line-height: 1.6;
        }

        .navbar {
            background: rgba(0, 191, 255, 0.1);
            border-bottom: 1px solid var(--accent-color);
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            backdrop-filter: blur(10px);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: 900;
            color: var(--accent-color);
            text-shadow: 0 0 10px var(--accent-color);
        }

        .nav-user {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-info {
            color: var(--text-secondary);
        }

        .btn {
            background: linear-gradient(45deg, var(--accent-color), #0080ff);
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 191, 255, 0.4);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid var(--text-secondary);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--spacing-xl);
        }

        .welcome-section {
            text-align: center;
            margin-bottom: var(--spacing-2xl);
        }

        .welcome-title {
            font-size: 3rem;
            color: var(--accent-color);
            text-shadow: 0 0 20px var(--accent-color);
            margin-bottom: var(--spacing-md);
            animation: glowPulse 3s ease-in-out infinite;
        }

        @keyframes glowPulse {
            0%, 100% { text-shadow: 0 0 20px var(--accent-color); }
            50% { text-shadow: 0 0 30px var(--accent-color), 0 0 40px var(--accent-color); }
        }

        .welcome-subtitle {
            font-size: 1.2rem;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-lg);
        }

        .grid {
            display: grid;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .grid-2 {
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        }

        .grid-3 {
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        }

        .card {
            background: rgba(0, 191, 255, 0.1);
            border: 1px solid var(--accent-color);
            border-radius: 16px;
            padding: var(--spacing-xl);
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 191, 255, 0.3);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card h2 {
            color: var(--accent-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.5rem;
        }

        .card h3 {
            color: var(--accent-color);
            margin-bottom: var(--spacing-sm);
            font-size: 1.2rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
            margin-top: var(--spacing-md);
        }

        .stat-item {
            text-align: center;
            padding: var(--spacing-md);
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .quick-actions {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }

        .action-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid var(--accent-color);
            padding: var(--spacing-md);
            border-radius: 8px;
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: left;
        }

        .action-btn:hover {
            background: rgba(0, 191, 255, 0.2);
            transform: translateX(5px);
        }

        .event-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-sm);
            border-left: 3px solid var(--accent-color);
        }

        .event-title {
            color: var(--accent-color);
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .event-details {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .alert-item {
            background: rgba(255, 170, 0, 0.1);
            border: 1px solid var(--warning-color);
            border-radius: 8px;
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-sm);
        }

        .alert-title {
            color: var(--warning-color);
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .alert-message {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .weather-widget {
            background: linear-gradient(135deg, rgba(0, 191, 255, 0.2), rgba(57, 255, 20, 0.1));
            border: 1px solid var(--accent-color);
            border-radius: 12px;
            padding: var(--spacing-lg);
            text-align: center;
        }

        .weather-temp {
            font-size: 2.5rem;
            color: var(--accent-color);
            font-weight: bold;
        }

        .weather-desc {
            color: var(--text-secondary);
            margin-top: 0.5rem;
        }

        .footer {
            text-align: center;
            padding: var(--spacing-xl);
            color: var(--text-secondary);
            border-top: 1px solid rgba(0, 191, 255, 0.3);
            margin-top: var(--spacing-2xl);
        }

        @media (max-width: 768px) {
            .navbar {
                padding: 1rem;
                flex-direction: column;
                gap: 1rem;
            }
            
            .container {
                padding: var(--spacing-md);
            }
            
            .welcome-title {
                font-size: 2rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }

        .loading {
            text-align: center;
            color: var(--text-secondary);
            padding: var(--spacing-xl);
        }

        .error {
            background: rgba(255, 71, 87, 0.1);
            border: 1px solid var(--error-color);
            color: var(--error-color);
            padding: var(--spacing-md);
            border-radius: 8px;
            margin: var(--spacing-md) 0;
        }
    </style>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Supabase -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="logo">NIMBUS</div>
        <div class="nav-user">
            <span class="user-info">Welcome, <span id="user-email">User</span></span>
            <button class="btn btn-secondary" onclick="logout()">Logout</button>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container">
        <!-- Welcome Section -->
        <div class="welcome-section">
            <h1 class="welcome-title">Welcome to Your Dashboard</h1>
            <p class="welcome-subtitle">Manage your events, track your activities, and stay connected with the cyberpunk community</p>
        </div>

        <!-- Loading State -->
        <div id="loading" class="loading">
            <p>🔄 Loading your dashboard...</p>
        </div>

        <!-- Main Dashboard Content -->
        <div id="dashboard-content" style="display: none;">
            <!-- Stats and Quick Actions Row -->
            <div class="grid grid-2">
                <!-- User Stats -->
                <div class="card">
                    <h2>📊 Your Stats</h2>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-number" style="color: var(--accent-color);" id="events-created">0</div>
                            <div class="stat-label">Events Created</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" style="color: var(--success-color);" id="events-attended">0</div>
                            <div class="stat-label">Events Attended</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" style="color: var(--warning-color);" id="saved-events">0</div>
                            <div class="stat-label">Saved Events</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" style="color: var(--error-color);" id="active-alerts">0</div>
                            <div class="stat-label">Active Alerts</div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card">
                    <h2>🎯 Quick Actions</h2>
                    <div class="quick-actions">
                        <button class="action-btn" onclick="createEvent()">
                            🎪 Create New Event
                        </button>
                        <button class="action-btn" onclick="browseEvents()">
                            🔍 Browse Events
                        </button>
                        <button class="action-btn" onclick="updatePreferences()">
                            ⚙️ Update Preferences
                        </button>
                        <button class="action-btn" onclick="viewWeather()">
                            🌦️ Weather Alerts
                        </button>
                        <button class="action-btn" onclick="viewProfile()">
                            👤 View Profile
                        </button>
                    </div>
                </div>
            </div>

            <!-- Events and Alerts Row -->
            <div class="grid grid-2">
                <!-- Plan Your Event -->
                <div class="card">
                    <h2>📅 Plan Your Event</h2>
                    <div id="upcoming-events">
                        <div class="loading">Loading events...</div>
                    </div>
                </div>

                <!-- Recent Alerts -->
                <div class="card">
                    <h2>🔔 Recent Alerts</h2>
                    <div id="recent-alerts">
                        <div class="loading">Loading alerts...</div>
                    </div>
                </div>
            </div>

            <!-- Weather and Activity Row -->
            <div class="grid grid-3">
                <!-- Weather Widget -->
                <div class="card">
                    <h2>🌤️ Weather</h2>
                    <div class="weather-widget">
                        <div class="weather-temp" id="weather-temp">22°C</div>
                        <div class="weather-desc" id="weather-desc">Partly Cloudy</div>
                        <div class="weather-desc" id="weather-location">Your Location</div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="card">
                    <h2>📈 Recent Activity</h2>
                    <div id="recent-activity">
                        <div class="loading">Loading activity...</div>
                    </div>
                </div>

                <!-- Recommendations -->
                <div class="card">
                    <h2>💡 Recommendations</h2>
                    <div id="recommendations">
                        <div class="event-item">
                            <div class="event-title">🎵 Electronic Music Night</div>
                            <div class="event-details">Based on your interest in concerts</div>
                        </div>
                        <div class="event-item">
                            <div class="event-title">🏔️ Weekend Hiking Trip</div>
                            <div class="event-details">Perfect weather this weekend</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <p>&copy; 2025 Nimbus - Cyberpunk Event Planning Platform</p>
        <p>Built with 💙 for the future of event management</p>
    </footer>

    <script>
        // Supabase configuration
        const supabaseUrl = 'https://ogjjhakrquzhgtbxonjr.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9nampoYWtycXV6aGd0YnhvbmpyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTk0OTUwNTUsImV4cCI6MjA3NTA3MTA1NX0.C5fsoYz8lvYuMTVn_8oodRMjKt6bKUsYmxn_gy4dMc8';
        
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);
        let currentUser = null;

        // Initialize the dashboard
        async function initDashboard() {
            try {
                // Check if user is logged in
                const { data: { session } } = await supabase.auth.getSession();

                if (!session?.user) {
                    // Check if this is an OAuth callback with auth fragments
                    const hashParams = new URLSearchParams(window.location.hash.substring(1));
                    const urlParams = new URLSearchParams(window.location.search);

                    if (hashParams.get('access_token') || urlParams.get('code')) {
                        // Wait for Supabase to process OAuth callback
                        document.getElementById('loading').innerHTML = '<p>🔄 Processing authentication...</p>';

                        setTimeout(async () => {
                            const { data: { session: newSession } } = await supabase.auth.getSession();
                            if (newSession?.user) {
                                // Reload the page to start fresh
                                window.location.href = 'home.html';
                                return;
                            } else {
                                // Still no session, redirect to login
                                window.location.href = 'nimbus.html';
                                return;
                            }
                        }, 2000);
                        return;
                    }

                    // No session and no OAuth callback, redirect to login
                    window.location.href = 'nimbus.html';
                    return;
                }

                currentUser = session.user;

                // Display user info (handle Google OAuth users)
                const displayName = currentUser.user_metadata?.full_name ||
                                  currentUser.user_metadata?.name ||
                                  currentUser.email;
                document.getElementById('user-email').textContent = displayName;

                // Load dashboard data
                await loadDashboardData();
                
                // Show dashboard content
                document.getElementById('loading').style.display = 'none';
                document.getElementById('dashboard-content').style.display = 'block';

            } catch (error) {
                console.error('Error initializing dashboard:', error);
                showError('Failed to load dashboard. Please try refreshing the page.');
            }
        }

        async function loadDashboardData() {
            try {
                // Load user stats
                await loadUserStats();
                
                // Load upcoming events
                await loadUpcomingEvents();
                
                // Load recent alerts
                await loadRecentAlerts();
                
                // Load recent activity
                await loadRecentActivity();
                
                // Load weather data
                await loadWeatherData();

            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        }

        async function loadUserStats() {
            try {
                // Get events created by user
                const { data: createdEvents } = await supabase
                    .from('events')
                    .select('id')
                    .eq('created_by', currentUser.id);

                // Get saved events
                const { data: savedEvents } = await supabase
                    .from('saved_events')
                    .select('id')
                    .eq('user_id', currentUser.id);

                // Get active alerts
                const { data: alerts } = await supabase
                    .from('alerts')
                    .select('id')
                    .eq('user_id', currentUser.id)
                    .eq('is_read', false);

                // Update stats display
                document.getElementById('events-created').textContent = createdEvents?.length || 0;
                document.getElementById('events-attended').textContent = '0'; // Would need attendance tracking
                document.getElementById('saved-events').textContent = savedEvents?.length || 0;
                document.getElementById('active-alerts').textContent = alerts?.length || 0;

            } catch (error) {
                console.error('Error loading user stats:', error);
            }
        }

        async function loadUpcomingEvents() {
            try {
                const { data: events, error } = await supabase
                    .from('events')
                    .select('*')
                    .gte('event_date', new Date().toISOString())
                    .order('event_date', { ascending: true })
                    .limit(5);

                const eventsContainer = document.getElementById('upcoming-events');
                
                if (events && events.length > 0) {
                    eventsContainer.innerHTML = events.map(event => `
                        <div class="event-item">
                            <div class="event-title">${event.title}</div>
                            <div class="event-details">
                                📍 ${event.location}<br>
                                📅 ${new Date(event.event_date).toLocaleDateString()}<br>
                                🏷️ ${event.activity_type}
                            </div>
                        </div>
                    `).join('');
                } else {
                    eventsContainer.innerHTML = '<p style="color: var(--text-secondary);">No upcoming events found.</p>';
                }

            } catch (error) {
                console.error('Error loading events:', error);
                document.getElementById('upcoming-events').innerHTML = '<p class="error">Failed to load events.</p>';
            }
        }

        async function loadRecentAlerts() {
            try {
                const { data: alerts, error } = await supabase
                    .from('alerts')
                    .select('*')
                    .eq('user_id', currentUser.id)
                    .order('created_at', { ascending: false })
                    .limit(3);

                const alertsContainer = document.getElementById('recent-alerts');
                
                if (alerts && alerts.length > 0) {
                    alertsContainer.innerHTML = alerts.map(alert => `
                        <div class="alert-item">
                            <div class="alert-title">${alert.title}</div>
                            <div class="alert-message">${alert.message}</div>
                        </div>
                    `).join('');
                } else {
                    alertsContainer.innerHTML = '<p style="color: var(--text-secondary);">No recent alerts.</p>';
                }

            } catch (error) {
                console.error('Error loading alerts:', error);
                document.getElementById('recent-alerts').innerHTML = '<p class="error">Failed to load alerts.</p>';
            }
        }

        async function loadRecentActivity() {
            try {
                const { data: activities, error } = await supabase
                    .from('user_activity')
                    .select('*')
                    .eq('user_id', currentUser.id)
                    .order('created_at', { ascending: false })
                    .limit(5);

                const activityContainer = document.getElementById('recent-activity');
                
                if (activities && activities.length > 0) {
                    activityContainer.innerHTML = activities.map(activity => `
                        <div class="event-item">
                            <div class="event-title">${activity.action.replace('_', ' ').toUpperCase()}</div>
                            <div class="event-details">
                                ${new Date(activity.created_at).toLocaleDateString()}
                            </div>
                        </div>
                    `).join('');
                } else {
                    activityContainer.innerHTML = '<p style="color: var(--text-secondary);">No recent activity.</p>';
                }

            } catch (error) {
                console.error('Error loading activity:', error);
                document.getElementById('recent-activity').innerHTML = '<p class="error">Failed to load activity.</p>';
            }
        }

        async function loadWeatherData() {
            // Mock weather data for demo
            document.getElementById('weather-temp').textContent = '22°C';
            document.getElementById('weather-desc').textContent = 'Perfect for outdoor events';
            document.getElementById('weather-location').textContent = 'Your Location';
        }

        // Action functions
        function createEvent() {
            alert('🎪 Create Event feature coming soon!');
        }

        function browseEvents() {
            alert('🔍 Browse Events feature coming soon!');
        }

        function updatePreferences() {
            alert('⚙️ Update Preferences feature coming soon!');
        }

        function viewWeather() {
            alert('🌦️ Weather Alerts feature coming soon!');
        }

        function viewProfile() {
            alert('👤 View Profile feature coming soon!');
        }

        async function logout() {
            try {
                const { error } = await supabase.auth.signOut();
                if (error) {
                    console.error('Error signing out:', error);
                } else {
                    window.location.href = 'nimbus.html';
                }
            } catch (error) {
                console.error('Logout error:', error);
            }
        }

        function showError(message) {
            document.getElementById('loading').innerHTML = `<div class="error">${message}</div>`;
        }

        // Initialize dashboard when page loads
        window.addEventListener('load', initDashboard);

        // Listen for auth state changes
        supabase.auth.onAuthStateChange((event, session) => {
            if (event === 'SIGNED_OUT') {
                window.location.href = 'nimbus.html';
            }
        });
    </script>
</body>
</html>
